<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Button Test</title>
</head>
<body>
    <h1>Simple Button Test</h1>
    
    <div class="pet-card">
        <div class="pet-avatar">🐱</div>
        <h3 class="pet-name">Test Pet</h3>
        <div class="pet-actions">
            <button class="action-btn feed-btn">Feed</button>
            <button class="action-btn play-btn">Play</button>
            <button class="action-btn sleep-btn">Sleep</button>
        </div>
    </div>

    <script>
        console.log('Test script loaded');
        
        document.addEventListener("DOMContentLoaded", function () {
            console.log('DOM loaded');
            
            const feedBtn = document.querySelector('.feed-btn');
            const playBtn = document.querySelector('.play-btn');
            const sleepBtn = document.querySelector('.sleep-btn');
            
            console.log('Buttons found:', {
                feed: !!feedBtn,
                play: !!playBtn,
                sleep: !!sleepBtn
            });
            
            if (feedBtn) {
                feedBtn.onclick = function() {
                    alert('Feed button works!');
                };
            }
            
            if (playBtn) {
                playBtn.onclick = function() {
                    alert('Play button works!');
                };
            }
            
            if (sleepBtn) {
                sleepBtn.onclick = function() {
                    alert('Sleep button works!');
                };
            }
        });
    </script>
</body>
</html>
