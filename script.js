// Navigation functionality
document.addEventListener("DOMContentLoaded", function () {
  const navButtons = document.querySelectorAll(".nav-btn");
  const sections = document.querySelectorAll(".content-section");

  navButtons.forEach((button) => {
    button.addEventListener("click", () => {
      const targetSection = button.getAttribute("data-section");

      // Hide all sections
      sections.forEach((section) => {
        section.classList.remove("active");
      });

      // Show target section
      document.getElementById(targetSection).classList.add("active");

      // Update active nav button
      navButtons.forEach((btn) => btn.classList.remove("active"));
      button.classList.add("active");
    });
  });

  // Pet interaction functionality
  const petCards = document.querySelectorAll(".pet-card");

  petCards.forEach((card) => {
    const feedBtn = card.querySelector(".feed-btn");
    const playBtn = card.querySelector(".play-btn");
    const sleepBtn = card.querySelector(".sleep-btn");
    const petAvatar = card.querySelector(".pet-avatar");
    const happinessFill = card.querySelector(".stat-fill");

    feedBtn.addEventListener("click", () => {
      petAvatar.style.transform = "scale(1.2)";
      setTimeout(() => {
        petAvatar.style.transform = "scale(1)";
      }, 300);

      showMessage("Yummy! 🍖");
      updateHappiness(card, 10);
    });

    playBtn.addEventListener("click", () => {
      petAvatar.style.animation = "none";
      setTimeout(() => {
        petAvatar.style.animation = "bounce 0.5s ease-in-out 3";
      }, 10);

      showMessage("Woof! So fun! 🎾");
      updateHappiness(card, 15);
    });

    sleepBtn.addEventListener("click", () => {
      petAvatar.style.opacity = "0.5";
      setTimeout(() => {
        petAvatar.style.opacity = "1";
      }, 2000);

      showMessage("Zzz... 😴");
      updateHappiness(card, 5);
    });
  });

  // Shop functionality
  const buyButtons = document.querySelectorAll(".buy-btn");

  buyButtons.forEach((button) => {
    button.addEventListener("click", (e) => {
      const item = e.target.closest(".shop-item");
      const itemName = item.querySelector(".item-name").textContent;

      button.textContent = "Purchased!";
      button.style.background = "rgba(72, 187, 120, 0.8)";

      setTimeout(() => {
        button.textContent = "Buy Now";
        button.style.background = "rgba(255,255,255,0.2)";
      }, 2000);

      showMessage(`You bought ${itemName}! 🛍️`);
    });
  });

  // Add new pet functionality
  const addPetBtn = document.querySelector(".add-pet-btn");
  const petGrid = document.querySelector(".pet-grid");

  addPetBtn.addEventListener("click", () => {
    const petEmojis = ["🐰", "🐹", "🐦", "🐠", "🐢", "🦔"];
    const petNames = [
      "Whiskers",
      "Nibbles",
      "Chirpy",
      "Bubbles",
      "Shelly",
      "Spike",
    ];

    const randomEmoji = petEmojis[Math.floor(Math.random() * petEmojis.length)];
    const randomName = petNames[Math.floor(Math.random() * petNames.length)];

    const newPetCard = createPetCard(randomEmoji, randomName);
    petGrid.appendChild(newPetCard);

    showMessage(`Welcome ${randomName}! 🎉`);

    // Add event listeners to new pet
    addPetEventListeners(newPetCard);
  });

  function createPetCard(emoji, name) {
    const petCard = document.createElement("div");
    petCard.className = "pet-card";
    petCard.innerHTML = `
            <div class="pet-avatar">${emoji}</div>
            <h3 class="pet-name">${name}</h3>
            <div class="pet-stats">
                <div class="stat">
                    <span class="stat-label">Happiness:</span>
                    <div class="stat-bar"><div class="stat-fill" style="width: 50%"></div></div>
                </div>
                <div class="stat">
                    <span class="stat-label">Energy:</span>
                    <div class="stat-bar"><div class="stat-fill" style="width: 70%"></div></div>
                </div>
            </div>
            <div class="pet-actions">
                <button class="action-btn feed-btn">Feed</button>
                <button class="action-btn play-btn">Play</button>
                <button class="action-btn sleep-btn">Sleep</button>
            </div>
        `;
    return petCard;
  }

  function addPetEventListeners(card) {
    const feedBtn = card.querySelector(".feed-btn");
    const playBtn = card.querySelector(".play-btn");
    const sleepBtn = card.querySelector(".sleep-btn");
    const petAvatar = card.querySelector(".pet-avatar");

    feedBtn.addEventListener("click", () => {
      petAvatar.style.transform = "scale(1.2)";
      setTimeout(() => (petAvatar.style.transform = "scale(1)"), 300);
      showMessage("Nom nom! 🍖");
      updateHappiness(card, 10);
    });

    playBtn.addEventListener("click", () => {
      petAvatar.style.animation = "bounce 0.5s ease-in-out 3";
      showMessage("Wheee! 🎾");
      updateHappiness(card, 15);
    });

    sleepBtn.addEventListener("click", () => {
      petAvatar.style.opacity = "0.5";
      setTimeout(() => (petAvatar.style.opacity = "1"), 2000);
      showMessage("Sweet dreams! 😴");
      updateHappiness(card, 5);
    });
  }

  function updateHappiness(card, amount) {
    const happinessFill = card.querySelector(".stat-fill");
    const currentWidth = parseInt(happinessFill.style.width) || 50;
    const newWidth = Math.min(100, currentWidth + amount);
    happinessFill.style.width = newWidth + "%";
  }

  function showMessage(text) {
    const message = document.createElement("div");
    message.textContent = text;
    message.style.cssText = `
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(0,0,0,0.8);
            color: white;
            padding: 1rem 2rem;
            border-radius: 25px;
            font-size: 1.2rem;
            z-index: 1000;
            animation: messagePopup 2s ease-in-out forwards;
        `;

    document.body.appendChild(message);

    setTimeout(() => {
      document.body.removeChild(message);
    }, 2000);
  }

  // Add CSS animation for message popup
  const style = document.createElement("style");
  style.textContent = `
        @keyframes messagePopup {
            0% { opacity: 0; transform: translate(-50%, -50%) scale(0.5); }
            20% { opacity: 1; transform: translate(-50%, -50%) scale(1.1); }
            100% { opacity: 0; transform: translate(-50%, -50%) scale(1); }
        }
    `;
  document.head.appendChild(style);
});
