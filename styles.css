* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Comic Sans MS', cursive, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    color: #333;
}

.main-header {
    background: rgba(255, 255, 255, 0.95);
    padding: 1rem 2rem;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    position: sticky;
    top: 0;
    z-index: 100;
}

.site-title {
    text-align: center;
    font-size: 2.5rem;
    color: #4a5568;
    margin-bottom: 1rem;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
}

.navigation {
    display: flex;
    justify-content: center;
    gap: 1rem;
}

.nav-btn {
    background: linear-gradient(45deg, #ff6b6b, #feca57);
    border: none;
    padding: 0.8rem 1.5rem;
    border-radius: 25px;
    color: white;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 1rem;
}

.nav-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.2);
}

.container {
    max-width: 1200px;
    margin: 2rem auto;
    padding: 0 1rem;
}

.content-section {
    display: none;
    background: rgba(255, 255, 255, 0.9);
    border-radius: 20px;
    padding: 2rem;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
}

.content-section.active {
    display: block;
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.section-title {
    text-align: center;
    font-size: 2rem;
    color: #2d3748;
    margin-bottom: 2rem;
    position: relative;
}

.section-title::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 100px;
    height: 3px;
    background: linear-gradient(45deg, #ff6b6b, #feca57);
    border-radius: 2px;
}

.pet-grid, .tips-grid, .shop-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin-bottom: 2rem;
}

.pet-card, .tip-card, .shop-item {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 15px;
    padding: 1.5rem;
    text-align: center;
    color: white;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.pet-card::before, .tip-card::before, .shop-item::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
    transform: rotate(45deg);
    transition: all 0.5s ease;
    z-index: 1;
    pointer-events: none; /* This prevents the pseudo-element from blocking clicks */
}

.pet-card:hover, .tip-card:hover, .shop-item:hover {
    transform: translateY(-5px) scale(1.02);
    box-shadow: 0 15px 40px rgba(0,0,0,0.2);
}

.pet-avatar, .tip-icon, .item-image {
    font-size: 4rem;
    margin-bottom: 1rem;
    display: block;
    animation: bounce 2s infinite;
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
    40% { transform: translateY(-10px); }
    60% { transform: translateY(-5px); }
}

.pet-name, .item-name {
    font-size: 1.5rem;
    margin-bottom: 1rem;
    font-weight: bold;
}

.pet-stats {
    margin-bottom: 1.5rem;
}

.stat {
    margin-bottom: 0.5rem;
    text-align: left;
}

.stat-label {
    display: block;
    font-size: 0.9rem;
    margin-bottom: 0.3rem;
}

.stat-bar {
    background: rgba(255,255,255,0.3);
    height: 10px;
    border-radius: 5px;
    overflow: hidden;
}

.stat-fill {
    background: linear-gradient(45deg, #48bb78, #38a169);
    height: 100%;
    border-radius: 5px;
    transition: width 0.5s ease;
}

.pet-actions {
    display: flex;
    gap: 0.5rem;
    justify-content: center;
}

.action-btn, .buy-btn {
    background: rgba(255,255,255,0.2);
    border: 2px solid rgba(255,255,255,0.5);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: bold;
    position: relative;
    z-index: 10; /* Ensure buttons are above other elements */
}

.action-btn:hover, .buy-btn:hover {
    background: rgba(255,255,255,0.3);
    transform: scale(1.05);
}

.add-pet-section {
    text-align: center;
    margin-top: 2rem;
}

.add-pet-btn {
    background: linear-gradient(45deg, #48bb78, #38a169);
    border: none;
    color: white;
    padding: 1rem 2rem;
    border-radius: 30px;
    font-size: 1.2rem;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
}

.add-pet-btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 10px 25px rgba(0,0,0,0.2);
}

.tip-text {
    font-size: 1rem;
    line-height: 1.5;
    opacity: 0.9;
}

.item-price {
    font-size: 1.2rem;
    font-weight: bold;
    color: #feca57;
    margin-bottom: 1rem;
}

.site-footer {
    background: rgba(0,0,0,0.8);
    color: white;
    padding: 2rem;
    margin-top: 3rem;
}

.footer-content {
    max-width: 1200px;
    margin: 0 auto;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
}

.footer-text {
    font-size: 1rem;
}

.social-links {
    display: flex;
    gap: 1rem;
}

.social-icon {
    font-size: 1.5rem;
    cursor: pointer;
    transition: transform 0.3s ease;
}

.social-icon:hover {
    transform: scale(1.2) rotate(10deg);
}

@media (max-width: 768px) {
    .site-title {
        font-size: 2rem;
    }

    .navigation {
        flex-direction: column;
        align-items: center;
    }

    .pet-grid, .tips-grid, .shop-grid {
        grid-template-columns: 1fr;
    }

    .footer-content {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }
}